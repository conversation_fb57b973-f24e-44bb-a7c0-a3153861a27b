import torch
import json
import time
from pathlib import Path
from typing import List, Dict
import re
import chromadb
import streamlit as st
from llama_index.core import VectorStoreIndex, StorageContext, Settings, get_response_synthesizer
from llama_index.core.chat_engine import ContextChatEngine
from llama_index.core.memory import ChatMemoryBuffer
from llama_index.core.query_engine import RetrieverQueryEngine
from llama_index.core.schema import TextNode, Document
from llama_index.llms.huggingface import HuggingFaceLLM
from llama_index.embeddings.huggingface import HuggingFaceEmbedding
from llama_index.vector_stores.chroma import ChromaVectorStore
from llama_index.core import PromptTemplate
from llama_index.core.postprocessor import SentenceTransformerRerank
from llama_index.llms.openai_like import OpenAILike
from llama_index.core import get_response_synthesizer
from llama_index.core.retrievers import VectorIndexRetriever
from llama_index.retrievers.bm25 import BM25Retriever
from llama_index.core.retrievers import QueryFusionRetriever
import base64
import io
import nest_asyncio
from pypdf import PdfReader

# 应用nest_asyncio补丁，以在Streamlit等现有事件循环中运行异步LlamaIndex代码
nest_asyncio.apply()

# ================== Streamlit页面配置 ==================
st.set_page_config(
    page_title="南水北调水利问答助手",
    page_icon="assets/Picture/lixiahe.png",
    layout="centered",
    initial_sidebar_state="auto"
)


# 加载自定义CSS文件的函数
def load_css(file_path):
    try:
        with open(file_path, encoding='utf-8') as f:
            st.markdown(f"<style>{f.read()}</style>", unsafe_allow_html=True)
    except FileNotFoundError:
        st.error(f"CSS文件未找到: {file_path}")


def disable_streamlit_watcher():
    """Patch Streamlit to disable file watcher"""

    def _on_script_changed(_):
        return

    from streamlit import runtime
    runtime.get_instance()._on_script_changed = _on_script_changed


# 新增：生成文本文件的函数
def generate_single_qa_text(question, answer):
    """生成单次问答的文本文件，完全支持中文"""
    content = "南水北调水利问答\n\n"
    content += f"问题:\n{question}\n\n"
    content += f"回答:\n{answer}\n\n"
    content += f"生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}"

    return content.encode('utf-8')


# ================================ 配置类 ================================
class Config:
    EMBED_MODEL_PATH = r"D:\pythonProject\embedding_model\BAAI\bge-large-zh-v1___5"
    RERANK_MODEL_PATH = r"D:\pythonProject\llms\BAAI\bge-reranker-base"  # 新增重排序模型路径

    DATA_DIR = "./data"
    VECTOR_DB_DIR = "./chroma_db"
    PERSIST_DIR = "./storage"

    COLLECTION_NAME = "chinese_labor_laws"
    TOP_K = 10  # 扩大初筛范围，检索更多潜在相关的文档
    RERANK_TOP_K = 3  # 严格重排序，只保留最相关的3个文档，提高信噪比
    
    # 相关度过滤配置
    RELEVANCE_THRESHOLD = 0.3  # 相关度阈值，低于此值的文档将被过滤掉
    
    # 文本分块配置
    # 可选值: 'semantic_double_merge' (语义双重合并分块), 'semantic' (语义分块), 'sentence' (传统句子分块)
    CHUNKING_MODE = 'semantic'
    # 传统分块参数
    CHUNK_SIZE = 512
    CHUNK_OVERLAP = 300
    # 语义分块参数
    SEMANTIC_BREAKPOINT_THRESHOLD = 90  # 百分位数阈值，越低生成的节点越多
    # 语义双重合并分块参数
    SEMANTIC_DOUBLE_INITIAL_THRESHOLD = 0.4  # 初始分块阈值
    SEMANTIC_DOUBLE_APPENDING_THRESHOLD = 0.5  # 附加阈值
    SEMANTIC_DOUBLE_MERGING_THRESHOLD = 0.5  # 合并阈值
    SEMANTIC_DOUBLE_MAX_CHUNK_SIZE = 3000  # 最大块大小


# ================== 缓存资源初始化 ==================
@st.cache_resource(show_spinner="初始化模型中...")
def init_models():
    # 文本嵌入模型
    embed_model = HuggingFaceEmbedding(
        model_name=Config.EMBED_MODEL_PATH,
        device='cuda' if torch.cuda.is_available() else 'cpu'  # 指定设备
    )

    # 使用DeepSeek的OpenAI兼容API
    # llm = OpenAILike(
    #     model="deepseek-chat",  # 可选模型：glm-4, glm-3-turbo, characterglm等
    #     api_base="https://api.deepseek.com",  # 关键！必须指定此端点
    #     api_key="***********************************",
    #     context_window=128000,  # 按需调整（glm-4实际支持128K）
    #     is_chat_model=True,
    #     is_function_calling_model=False,  # GLM暂不支持函数调用
    #     max_tokens=4096,  # 最大生成token数（按需调整）
    #     temperature=0.3,  # 推荐范围 0.1~1.0
    #     top_p=0.7  # 推荐范围 0.5~1.0
    # )
        # 使用阿里云的OpenAI兼容API（提供统一的接口调用llm）
    try:
        llm = OpenAILike(
            model="qwen-plus",  # 阿里云千问模型
            api_base="https://dashscope.aliyuncs.com/compatible-mode/v1",  # 阿里云API端点
            api_key="sk-1e2ce235b22d40afb019864b3f29b803",  # 请确保此密钥有效
            context_window=128000,    # 上下文窗口大小
            is_chat_model=True,
            is_function_calling_model=False,
            max_tokens=1024,          # 最大生成token数
            temperature=0.3,          # 温度参数
            top_p=0.7,                # top_p参数
            timeout=60,               # 增加超时时间到60秒
            max_retries=5             # 增加重试次数到5次
        )
        print("✅ LLM初始化成功")
    except Exception as e:
        print(f"❌ LLM初始化失败: {str(e)}")
        raise e

    # 重排序模型
    reranker = SentenceTransformerRerank(
        model=Config.RERANK_MODEL_PATH,
        top_n=Config.RERANK_TOP_K
    )

    Settings.embed_model = embed_model
    Settings.llm = llm

    return embed_model, llm, reranker


# 新增：缓存NodeParser
@st.cache_resource(show_spinner="初始化文本分割器...")
def init_node_parser():
    """
    初始化语义感知文本分割器，根据内容语义自动调整块大小，避免语义割裂
    支持三种分块方式:
    1. 语义双重合并分块 (最先进，需要spaCy)
    2. 语义分块 (较先进)
    3. 传统句子分块 (回退方案)
    """
    # 从配置中获取分块模式
    chunking_mode = Config.CHUNKING_MODE
    
    try:
        if chunking_mode == 'semantic_double_merge':
            # 尝试导入语义双重合并分块所需的模块
            try:
                import spacy
                from llama_index.core.node_parser import (
                    SemanticDoubleMergingSplitterNodeParser,
                    LanguageConfig
                )
                
                # 检查是否已安装spaCy模型
                try:
                    nlp = spacy.load("zh_core_web_md")
                    print("成功加载中文spaCy模型")
                except OSError:
                    print("未找到中文spaCy模型，尝试使用英文模型")
                    try:
                        nlp = spacy.load("en_core_web_md")
                        print("成功加载英文spaCy模型")
                    except OSError:
                        raise ImportError("未找到所需的spaCy模型，请安装: python -m spacy download zh_core_web_md")
                
                # 配置语言设置
                language = "chinese" if "zh" in nlp.meta["lang"] else "english"
                config = LanguageConfig(
                    language=language,
                    spacy_model=nlp.meta["name"]
                )
                
                # 创建语义双重合并分块器，使用配置参数
                semantic_double_merger = SemanticDoubleMergingSplitterNodeParser(
                    language_config=config,
                    initial_threshold=Config.SEMANTIC_DOUBLE_INITIAL_THRESHOLD,
                    appending_threshold=Config.SEMANTIC_DOUBLE_APPENDING_THRESHOLD,
                    merging_threshold=Config.SEMANTIC_DOUBLE_MERGING_THRESHOLD,
                    max_chunk_size=Config.SEMANTIC_DOUBLE_MAX_CHUNK_SIZE
                )
                
                print(f"成功初始化语义双重合并分块器 (语言: {language})")
                return semantic_double_merger
                
            except (ImportError, Exception) as e:
                print(f"语义双重合并分块器初始化失败: {str(e)}，回退到语义分块器")
                chunking_mode = 'semantic'
        
        if chunking_mode == 'semantic':
            # 尝试导入语义分块所需的模块
            from llama_index.core.node_parser import SemanticSplitterNodeParser
            from llama_index.embeddings.huggingface import HuggingFaceEmbedding
            
            # 使用与系统相同的嵌入模型，保持一致性
            embed_model = HuggingFaceEmbedding(
                model_name=Config.EMBED_MODEL_PATH,
                device='cuda' if torch.cuda.is_available() else 'cpu'
            )
            
            # 创建语义分块器，使用配置参数
            semantic_splitter = SemanticSplitterNodeParser(
                buffer_size=1,  # 每次分析一个句子
                breakpoint_percentile_threshold=Config.SEMANTIC_BREAKPOINT_THRESHOLD,
                embed_model=embed_model
            )
            
            print("成功初始化语义分块器")
            return semantic_splitter
            
    except (ImportError, Exception) as e:
        print(f"语义分块器初始化失败: {str(e)}，回退到传统分块器")
    
    # 如果语义分块器初始化失败，回退到传统的SentenceSplitter
    from llama_index.core.node_parser import SentenceSplitter
    print("使用传统句子分块器")
    return SentenceSplitter(
        chunk_size=Config.CHUNK_SIZE,
        chunk_overlap=Config.CHUNK_OVERLAP
    )


@st.cache_resource(show_spinner="加载知识库中...")
def load_index():
    """
    根据官方文档推荐的最佳实践，从磁盘显式加载知识库的各个组件。
    """
    from llama_index.core.storage.docstore import SimpleDocumentStore
    persist_dir = Path(Config.PERSIST_DIR)
    db_dir = Path(Config.VECTOR_DB_DIR)
    docstore_path = persist_dir / "docstore.json"

    if not all([persist_dir.exists(), db_dir.exists(), docstore_path.exists()]):
        print("知识库目录或必要文件(docstore.json)不存在。")
        return None
    
    try:
        print("--- 正在加载知识库 ---")
        # 1. 显式加载向量数据库
        db = chromadb.PersistentClient(path=str(db_dir))
        chroma_collection = db.get_or_create_collection(Config.COLLECTION_NAME)
        vector_store = ChromaVectorStore(chroma_collection=chroma_collection)

        # 2. 显式加载文档库
        docstore = SimpleDocumentStore.from_persist_path(str(docstore_path))
        print(f"文档库加载成功，共 {len(docstore.docs)} 个文档。")

        # 3. 从加载的组件重建存储上下文和索引
        storage_context = StorageContext.from_defaults(
            docstore=docstore, vector_store=vector_store
        )
        # 修复：使用官方推荐的构造函数，而不是废弃的 from_storage
        index = VectorStoreIndex(nodes=[], storage_context=storage_context)
        
        print("--- 知识库加载成功！---")
        return index
    except Exception as e:
        print(f"加载知识库失败: {e}")
        import traceback
        traceback.print_exc()
        return None


# ============================== 数据处理 ==============================
# ===========数据处理json格式数据 ==========
def load_and_validate_json_files(data_dir: str) -> List[Dict]:
    """加载并验证JSON法律文件"""
    json_files = list(Path(data_dir).glob("*.json"))
    assert json_files, f"未找到JSON文件于 {data_dir}"

    all_data = []
    for json_file in json_files:
        with open(json_file, 'r', encoding='utf-8') as f:
            try:
                data = json.load(f)
                # 验证数据结构
                if not isinstance(data, list):
                    raise ValueError(f"文件 {json_file.name} 根元素应为列表")
                for item in data:
                    if not isinstance(item, dict):
                        raise ValueError(f"文件 {json_file.name} 包含非字典元素")
                    for k, v in item.items():
                        if not isinstance(v, str):
                            raise ValueError(f"文件 {json_file.name} 中键 '{k}' 的值不是字符串")
                all_data.extend({
                                    "content": item,
                                    "metadata": {"source_file": json_file.name, "content_type": "json_item"}
                                } for item in data)
            except Exception as e:
                raise RuntimeError(f"加载文件 {json_file} 失败: {str(e)}")

    print(f"成功加载 {len(all_data)} 个法律文件条目")
    return all_data

# ===========数据处理PDF格式数据 ==========
from pdf2image import convert_from_path
import pytesseract
from pathlib import Path
from typing import List, Dict

# 设置 Tesseract 路径（Windows 专用）
pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'
def load_pdfs(data_dir: str) -> List[Dict]:
    pdf_files = list(Path(data_dir).rglob("*.pdf"))
    all_data = []
    skipped_files = 0

    for pdf_file in pdf_files:
        try:
            images = convert_from_path(pdf_file, dpi=300)  # 高分辨率提高识别率
            text = ""
            for img in images:
                text += pytesseract.image_to_string(img, lang='chi_sim')  # 简体中文

            if not text.strip() or len(text.strip()) < 20:
                print(f"警告：OCR后PDF文件 {pdf_file.name} 仍无内容，已跳过。")
                skipped_files += 1
                continue

            all_data.append({
                "content": text,
                "metadata": {
                    "source_file": str(pdf_file.relative_to(data_dir)),
                    "content_type": "pdf_document"
                }
            })
        except Exception as e:
            print(f"OCR处理PDF文件 {pdf_file.name} 时出错: {str(e)}")
            skipped_files += 1

    print(f"OCR后成功加载 {len(all_data)} 个 PDF 文件")
    if skipped_files > 0:
        print(f"有 {skipped_files} 个PDF文件被跳过")

    return all_data


# ===========数据处理excel格式数据 ==========
import pandas as pd
from pathlib import Path
import concurrent.futures
from functools import lru_cache

def load_excels(data_dir: str) -> List[Dict]:
    """
    优化的Excel加载函数 v4。
    1. 使用并行处理加速多文件处理
    2. 使用LRU缓存减少重复计算
    3. 批量处理数据以提高效率
    4. 智能判断数据类型减少转换错误
    """
    excel_files = list(Path(data_dir).rglob("*.xlsx"))
    all_data = []
    
    # 定义日期格式的缓存函数
    @lru_cache(maxsize=128)
    def format_datetime(value_str):
        try:
            dt = pd.to_datetime(value_str, errors='coerce')
            if pd.notna(dt):
                return dt.strftime('%Y-%m-%d %H:%M:%S')
        except Exception:
            pass
        return value_str
    
    # 智能处理单个值
    def process_value(col, value_str):
        # 如果值为空，直接返回"未知"
        if pd.isna(value_str) or value_str == "":
            return "未知"
            
        # 处理日期时间类型
        if isinstance(value_str, str) and ('时间' in str(col) or '日期' in str(col)):
            return format_datetime(value_str)
            
        # 处理数值类型
        if isinstance(value_str, str):
            try:
                value_float = float(value_str)
                # 只对非整数进行四舍五入
                if value_float.is_integer():
                    return str(int(value_float))
                else:
                    return str(round(value_float, 3))
            except (ValueError, TypeError):
                pass
                
        return str(value_str)
    
    # 处理单个Excel文件
    def process_excel_file(excel_file):
        file_data = []
        try:
            # 使用engine='openpyxl'提高与新版Excel的兼容性
            sheets = pd.read_excel(excel_file, sheet_name=None, dtype=str, engine='openpyxl')
            
            for sheet_name, df in sheets.items():
                if df.empty:
                    continue
                
                # 清理列名
                df.columns = df.columns.map(str)
                columns = df.columns.tolist()
                
                # 预处理整个数据框，而不是逐行处理
                # 优化1: 批量处理时间列
                time_cols = [col for col in columns if '时间' in str(col) or '日期' in str(col)]
                for col in time_cols:
                    if col in df.columns:
                        df[col] = df[col].apply(lambda x: format_datetime(x) if pd.notna(x) and isinstance(x, str) else x)
                
                # 处理每行数据
                for idx, row in df.iterrows():
                    # 构建键值对列表
                    parts = [f"'{col}': '{process_value(col, row[col])}'" for col in columns]
                    
                    # 采用结构化文本格式
                    row_description = f"数据记录 - 文件: {excel_file.name}, 工作表: {sheet_name}, 行号: {idx + 2}. 内容: {{{', '.join(parts)}}}"
                    
                    file_data.append({
                        "content": row_description,
                        "metadata": {
                            "source_file": str(excel_file.relative_to(data_dir)),
                            "content_type": "excel_row",
                            "sheet_name": sheet_name,
                            "row_number": idx + 2
                        }
                    })
                    
        except Exception as e:
            print(f"加载或处理Excel文件 {excel_file} 时出错: {str(e)}")
        return file_data
    
    # 并行处理所有Excel文件
    with concurrent.futures.ThreadPoolExecutor(max_workers=min(8, len(excel_files))) as executor:
        results = list(executor.map(process_excel_file, excel_files))
    
    # 合并所有结果
    for result in results:
        all_data.extend(result)
    
    print(f"成功从Excel文件中加载并处理了 {len(all_data)} 行数据。")
    return all_data

import os
def create_nodes_from_text(raw_data: List[Dict], node_parser) -> List[TextNode]:
    all_nodes = []
    for entry in raw_data:
        content = entry["content"]
        source_file = entry["metadata"].get("source_file", "unknown_source")

        doc = Document(text=content, metadata={"source_file": source_file, "content_type": "text_document"})
        nodes = node_parser.get_nodes_from_documents([doc])

        for i, node in enumerate(nodes):
            node.id_ = f"{source_file}::chunk_{i}"
            node.metadata.update({
                "source_file": source_file,
                "content_type": "text_document_chunk"
            })
            all_nodes.append(node)

    return all_nodes


# ===========专门处理PDF文本的节点生成函数 ==========

# 添加专门处理PDF文件的节点生成函数
def create_nodes_from_pdf(raw_data: List[Dict], node_parser) -> List[TextNode]:
    all_nodes = []
    warning_count = 0

    for entry in raw_data:
        content = entry["content"]
        if not content or len(content.strip()) < 10:
            source = entry["metadata"].get("source_file", "未知文件")
            print(f"警告：PDF文件 {source} 内容为空，已跳过")
            warning_count += 1
            continue

        source_file = entry["metadata"]["source_file"]

        doc = Document(text=content, metadata={
            "source_file": source_file,
            "content_type": "pdf_document"
        })

        try:
            nodes = node_parser.get_nodes_from_documents([doc])
            for i, node in enumerate(nodes):
                node.id_ = f"{source_file}::chunk_{i}"
                node.metadata.update({
                    "source_file": source_file,
                    "content_type": "pdf_document_chunk"
                })
                all_nodes.append(node)
        except Exception as e:
            print(f"处理PDF文件 {source_file} 时出错: {str(e)}")

    if warning_count > 0:
        print(f"注意: {warning_count} 个PDF文件因内容为空被跳过")

    print(f"成功从PDF内容生成 {len(all_nodes)} 个文本节点。")
    return all_nodes
def create_nodes_from_excel(raw_data: List[Dict], node_parser) -> List[TextNode]:
    """
    为Excel的每一行数据创建一个TextNode。
    优化处理以更高效地创建节点并保持唯一ID。
    支持并行处理以加快处理速度。
    """
    if not raw_data:
        print("警告: 没有Excel数据可以处理")
        return []
    
    all_nodes = []
    excel_entries = [entry for entry in raw_data if entry.get("metadata", {}).get("content_type") == "excel_row"]
    
    # 按文件和工作表分组，以便更好地组织节点
    file_sheet_groups = {}
    for entry in excel_entries:
        metadata = entry["metadata"]
        file_key = metadata.get("source_file", "unknown_file")
        sheet_key = metadata.get("sheet_name", "unknown_sheet")
        key = (file_key, sheet_key)
        
        if key not in file_sheet_groups:
            file_sheet_groups[key] = []
        file_sheet_groups[key].append(entry)
    
    # 处理每个文件-工作表组
    for (file_key, sheet_key), entries in file_sheet_groups.items():
        # 为每个组创建统一的前缀
        prefix = f"{file_key}::{sheet_key}"
        
        # 创建节点
        for entry in entries:
            metadata = entry["metadata"]
            row_number = metadata.get("row_number", "unknown_row")
            node_id = f"{prefix}::row_{row_number}"
            
            node = TextNode(
                text=entry["content"],
                id_=node_id,
                metadata=metadata
            )
            all_nodes.append(node)
    
    print(f"从Excel数据中创建了 {len(all_nodes)} 个文本节点")
    return all_nodes


# ================== 界面组件 ==================
# 修改: 使用自定义的HTML/CSS渲染聊天气泡
def display_chat_message(message):
    """根据角色渲染用户或助手的聊天气泡"""
    role = message["role"]
    content = message.get("cleaned", message["content"])

    if role == "user":
        st.markdown(f'<div class="user-bubble">{content}</div>', unsafe_allow_html=True)
    elif role == "assistant":
        # 为每个消息创建唯一的键，避免UI元素ID冲突
        message_id = f"msg_{id(message)}"
        
        # 1. 显示回答文本
        st.markdown(f'<div class="assistant-bubble">{content}</div>', unsafe_allow_html=True)
        
        # 2. 首先展示模型思考过程
        if message.get("think"):
            # 创建一个唯一的会话状态键，用于控制模型思考过程的展开状态
            thinking_key = f"think_expanded_{message_id}"
            if thinking_key not in st.session_state:
                st.session_state[thinking_key] = False
                
            # 使用Streamlit按钮来控制展开状态
            if st.button(
                "💭 查看模型思考过程" if not st.session_state[thinking_key] else "💭 隐藏模型思考过程",
                key=f"thinking_btn_{message_id}",
                type="primary"
            ):
                st.session_state[thinking_key] = not st.session_state[thinking_key]
                st.rerun()  # 重新加载页面以更新展开状态
            
            # 基于会话状态显示思考内容
            if st.session_state[thinking_key]:
                st.markdown("### 模型思考过程")
                for i, think_content in enumerate(message["think"], 1):
                    st.markdown(f"""
                    <div class="thinking-step">
                        <div class="thinking-header">思考步骤 {i}</div>
                        <div class="thinking-content">{think_content.strip()}</div>
                    </div>
                    """, unsafe_allow_html=True)

        # 3. 然后显示参考文献
        if "reference_nodes" in message and message["reference_nodes"]:
            reference_key = f"ref_expanded_{message_id}"
            if reference_key not in st.session_state:
                st.session_state[reference_key] = False
                
            # 使用Streamlit按钮来控制参考文献的展开状态
            if st.button(
                "📚 查看参考文献" if not st.session_state[reference_key] else "📚 隐藏参考文献",
                key=f"ref_btn_{message_id}",
                type="secondary"
            ):
                st.session_state[reference_key] = not st.session_state[reference_key]
                st.rerun()  # 重新加载页面以更新展开状态
                
            # 基于会话状态显示参考文献
            if st.session_state[reference_key]:
                st.markdown("### 参考文献")
                for idx, node in enumerate(message["reference_nodes"], 1):
                    meta = node.node.metadata
                    source_file = meta.get("source_file", "未知文件")
                    
                    st.markdown(f"""
                    <div class="reference-expander">
                        <p><b>[{idx}] 来源:</b> {source_file}</p>
                        <p><b>相关度:</b> {node.score:.4f}</p>
                        <p><b>内容片段:</b></p>
                        <blockquote>{node.node.text}</blockquote>
                    </div>
                    """, unsafe_allow_html=True)

    # 添加一个清除浮动的div，防止布局错乱
    st.markdown('<div style="clear: both;"></div>', unsafe_allow_html=True)


def init_chat_interface():
    if "messages" not in st.session_state:
        st.session_state.messages = []

    for msg in st.session_state.messages:
        # 使用display_chat_message函数统一显示所有消息
        display_chat_message(msg)


# 这部分功能已移至display_chat_message函数内直接实现


# 新增：记录系统无法回答的问题
def log_unanswerable_question(question: str, context_nodes=None):
    """
    记录系统无法回答的问题，以便后续改进知识库
    
    Args:
        question: 用户提问的问题
        context_nodes: 检索到的上下文节点（如果有）
    """
    try:
        log_dir = Path("./logs")
        log_dir.mkdir(exist_ok=True)
        
        log_file = log_dir / "unanswerable_questions.jsonl"
        
        # 准备日志条目
        log_entry = {
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "question": question,
            "retrieved_contexts": []
        }
        
        # 如果有上下文节点，记录它们的信息
        if context_nodes:
            for node in context_nodes:
                log_entry["retrieved_contexts"].append({
                    "text": node.node.text[:200] + "..." if len(node.node.text) > 200 else node.node.text,
                    "source": node.node.metadata.get("source_file", "未知来源"),
                    "score": node.score if hasattr(node, "score") else None
                })
        
        # 追加到日志文件
        with open(log_file, "a", encoding="utf-8") as f:
            f.write(json.dumps(log_entry, ensure_ascii=False) + "\n")
            
        print(f"已记录无法回答的问题: {question}")
    except Exception as e:
        print(f"记录无法回答的问题时出错: {str(e)}")


# ================== 主程序 ==================
def main():
    load_css("frontend/style.css")

    # 初始化按钮计数器
    if "button_counter" not in st.session_state:
        st.session_state.button_counter = 0

    # 确保messages已初始化
    if "messages" not in st.session_state:
        st.session_state.messages = []

    # --- 侧边栏 ---
    with st.sidebar:
        st.image("assets/Picture/lixiahe.png", width=80)
        st.title("控制与信息")
        st.info("欢迎使用南水北调水利问答助手。本系统旨在提供专业、准确的信息。")

        # 清除聊天记录按钮 - 添加唯一key
        if st.button("清除聊天记录", use_container_width=True, type="primary", key="clear_chat_button"):
            st.session_state.messages = []
            # 同时清除后端的对话内存
            if "chat_engine" in st.session_state:
                st.session_state.chat_engine.reset()
            st.rerun()

        # 导出整个对话为文本文件 - 添加唯一key
        if st.session_state.messages and st.button("导出对话为文本", use_container_width=True,
                                                   key="export_all_text_button"):
            try:
                content = "南水北调水利问答记录\n\n"

                for msg in st.session_state.messages:
                    role = msg["role"]
                    text = msg.get("cleaned", msg["content"])

                    if role == "user":
                        content += f"问题:\n{text}\n\n"
                    elif role == "assistant":
                        content += f"回答:\n{text}\n\n"

                content += f"生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}"
                text_bytes = content.encode('utf-8')

                timestamp = time.strftime("%Y%m%d-%H%M%S")
                b64 = base64.b64encode(text_bytes).decode()
                href = f'<a href="data:text/plain;charset=utf-8;base64,{b64}" download="南水北调问答-{timestamp}.txt">点击下载文本文件</a>'
                st.markdown(href, unsafe_allow_html=True)
            except Exception as e:
                st.error(f"生成文本时出错: {str(e)}")

    # --- 主界面 ---
    st.title("南水北调水利问答助手 💧")
    st.markdown("请输入您的问题，我们将基于最新的研究成果和工程实践为您解答。")

    embed_model, llm, reranker = init_models()
    # 用下面这行代码替换掉原来复杂的if/else数据加载逻辑
    index = load_index()

    # 如果加载失败，显示错误并停止应用
    if index is None:
        st.error(
            "未能加载知识库。请先在终端运行 'python build_knowledge_base.py' 来构建知识库。"
        )
        st.stop()  # 停止执行


    # --- 对话引擎核心修改：暂时使用简单检索器避免API连接问题 ---
    if "chat_engine" not in st.session_state:
        with st.spinner("正在初始化对话引擎..."):
            # 暂时使用简单的向量检索器，避免QueryFusionRetriever的API调用问题
            retriever = index.as_retriever(similarity_top_k=Config.TOP_K)

            # 如果需要使用融合检索，可以在API连接稳定后启用以下代码：
            # vector_retriever = index.as_retriever(similarity_top_k=Config.TOP_K)
            # nodes = list(index.docstore.docs.values())
            # if nodes:
            #     bm25_retriever = BM25Retriever.from_defaults(
            #         nodes=nodes,
            #         similarity_top_k=Config.TOP_K
            #     )
            #     retriever = QueryFusionRetriever(
            #         retrievers=[vector_retriever, bm25_retriever],
            #         similarity_top_k=Config.TOP_K,
            #         num_queries=4,
            #         use_async=True,
            #         verbose=True
            #     )

            # 3. 定义并保存我们最严格的提示词模板
            QA_PROMPT_TMPL_STR = (
                "你是一个高度严谨的南水北调水利问答机器人，你的任务是只使用下面提供的上下文信息来回答问题。\n"
                "**规则:**\n"
                "1. 你的回答必须完全且仅基于上下文信息，严禁使用任何外部知识或进行推测。\n"
                "2. 对于你的回答中的每一个观点、事实或数据，都必须在句末明确标注其来源，格式为 `[引用来源 n]`，其中n是上下文信息中对应条目的编号。\n"
                "3. 如果多个来源支持同一个观点，可以标注多个来源，如 `[引用来源 1, 3]`。\n"
                "4. 如果上下文中没有足够信息回答问题，必须直接回答：'根据提供的资料，我无法回答这个问题。' 不要尝试推测或使用你的背景知识。\n"
                "5. 如果上下文中的信息与问题相关但不足以完整回答，请明确指出哪些方面的信息是可用的，哪些是缺失的。\n"
                "6. 如果问题完全超出南水北调工程和水利领域，请回答：'这个问题超出了我的专业范围，我主要负责回答关于南水北调工程和水利相关的问题。'\n\n"
                "**上下文信息:**\n"
                "---------------------\n"
                "{context_str}\n"
                "---------------------\n"
                "**思考过程:**\n"
                "在回答前，你必须首先在 <think> 和 </think> 标签之间详细展示你的分析过程。这是强制性的，不可跳过。\n"
                "你的思考过程必须包含以下步骤，并明确标注每一步：\n"
                "1. 问题分析：首先分析用户问题的核心意图和关键要素\n"
                "2. 上下文评估：逐一分析每个上下文片段与问题的相关性，指出最重要的信息来源\n"
                "3. 信息充分性：评估上下文信息是否足够回答问题，如果信息不足，明确指出缺失的部分\n"
                "4. 回答规划：制定一个严格遵循引用规则的回答计划，注明将如何组织信息\n"
                "5. 总结：对你将如何回答问题进行总体概括\n\n"
                "示例格式：\n<think>\n步骤1-问题分析: [你的分析内容...]\n\n步骤2-上下文评估: [你的评估内容...]\n\n[其他步骤...]\n</think>\n\n"
                "请确保思考过程放在回答之前，且必须用<think>和</think>标签包围，这对系统正常工作至关重要。\n"
                "**回答:**\n"
                "问题: {query_str}\n"
            )

            qa_prompt_tmpl = PromptTemplate(QA_PROMPT_TMPL_STR)

            # 添加相关度阈值过滤器，确保只有相关度足够高的文档才被用于回答问题
            class RelevanceScoreNodePostprocessor:
                """过滤掉相关度低于阈值的节点"""
                def __init__(self, threshold=0.3):
                    self.threshold = threshold
                    
                def postprocess_nodes(self, nodes, query_bundle):
                    # 过滤掉相关度低于阈值的节点
                    filtered_nodes = [node for node in nodes if node.score >= self.threshold]
                    
                    # 如果过滤后没有节点，返回一个空列表，这将触发"无法回答"逻辑
                    if not filtered_nodes and nodes:
                        print(f"警告：所有检索到的节点相关度都低于阈值 {self.threshold}，将返回空结果")
                    
                    return filtered_nodes

            # 创建相关度过滤器，设置阈值
            relevance_filter = RelevanceScoreNodePostprocessor(
                threshold=Config.RELEVANCE_THRESHOLD
            )

            # 4. 使用强大的融合检索器创建带记忆的聊天引擎
            st.session_state.chat_engine = ContextChatEngine.from_defaults(
                retriever=retriever,
                llm=llm,
                memory=ChatMemoryBuffer.from_defaults(token_limit=4096),
                node_postprocessors=[reranker, relevance_filter],  # 先重排序，再过滤低相关度节点
                context_prompt=qa_prompt_tmpl,
                verbose=True
            )

    # --- 聊天界面 ---
    # 显示历史消息
    for msg in st.session_state.messages:
        display_chat_message(msg)

    # 处理用户输入
    if prompt := st.chat_input("请输入您的问题..."):
        # 将用户消息添加到显示列表并立即显示
        user_message = {"role": "user", "content": prompt}
        st.session_state.messages.append(user_message)
        display_chat_message(user_message)

        # 处理查询并显示助手响应
        with st.spinner("正在使用融合检索模式检索并生成回答..."):
            # 直接使用我们强大的融合检索聊天引擎进行对话
            response = st.session_state.chat_engine.chat(prompt)
            response_text = str(response)

            # 提取思考过程和参考文献
            think_contents = re.findall(r'<think>(.*?)</think>', response_text, re.DOTALL)
            cleaned_response = re.sub(r'<think>.*?</think>', '', response_text, flags=re.DOTALL).strip()
            
            # 打印调试信息，帮助诊断
            print(f"\n\n===== 调试信息 =====")
            print(f"模型回复原始文本长度: {len(response_text)}")
            print(f"提取到的思考过程数量: {len(think_contents)}")
            if not think_contents:
                print("警告: 未能提取到模型思考过程。可能原因:")
                print("1. 模型没有按照提示词要求生成<think>标签内容")
                print("2. 所使用的模型不支持此格式的思考过程输出")
                print("3. 提示词模板需要调整以更明确地引导模型输出思考过程")
                
                # 如果无法从回复中提取思考过程，我们手动创建一个
                if not think_contents and "<think>" not in response_text:
                    # 为确保界面能显示按钮，添加一个默认思考过程
                    think_contents = ["模型未输出详细思考过程。我们正在调整系统，以便未来能够显示更详细的思考过程。"]
                    print("已添加默认思考过程，确保界面按钮显示")
            
            # 从响应对象中获取源节点
            source_nodes = response.source_nodes
            
            # 检查是否无法回答问题
            if "我无法回答这个问题" in cleaned_response or "超出了我的专业范围" in cleaned_response:
                # 记录无法回答的问题
                log_unanswerable_question(prompt, source_nodes)

            # 创建助手消息对象，确保始终包含思考过程
            assistant_message = {
                "role": "assistant",
                "content": response_text,
                "cleaned": cleaned_response,
                "think": think_contents if think_contents else ["模型思考过程提取失败。我们正在改进提示词模板，以便未来显示更详细的思考过程。"],
                "reference_nodes": source_nodes
            }
            
            # 记录调试信息
            print(f"助手消息已创建，包含 {len(assistant_message['think'])} 条思考过程")
            st.session_state.messages.append(assistant_message)

            # 保存当前问答对，用于文本导出
            st.session_state.last_qa = {
                "question": prompt,
                "answer": cleaned_response
            }

            # 使用rerun来刷新界面并显示助手的完整消息
            st.rerun()

    # 显示下载按钮（对于最近的一次对话）
    if "last_qa" in st.session_state:
        col1, col2 = st.columns([3, 1])
        with col2:
            # 直接准备文本内容，不需要额外的按钮点击
            q = st.session_state.last_qa["question"]
            a = st.session_state.last_qa["answer"]
            content = generate_single_qa_text(q, a).decode('utf-8')
            timestamp = time.strftime("%Y%m%d-%H%M%S")

            # 使用与侧边栏相同的下载按钮实现
            st.download_button(
                label="📄 导出此问答",
                data=content,
                file_name=f"南水北调问答-{timestamp}.txt",
                mime="text/plain",
                key=f"download_single_{len(st.session_state.messages)}_{timestamp}"
            )

if __name__ == "__main__":
    main()