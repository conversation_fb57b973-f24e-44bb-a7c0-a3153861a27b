<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="736dcd87-0fb5-4a1e-8599-48c618c94e2c" name="更改" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Python Script" />
      </list>
    </option>
  </component>
  <component name="FlaskConsoleOptions" custom-start-script="import sys&#10;sys.path.extend([WORKING_DIR_AND_PYTHON_PATHS])&#10;from flask.cli import ScriptInfo&#10;locals().update(ScriptInfo(create_app=None).load_app().make_shell_context())&#10;print(&quot;Python %s on %s\nApp: %s [%s]\nInstance: %s&quot; % (sys.version, sys.platform, app.import_name, app.env, app.instance_path))">
    <envs>
      <env key="FLASK_APP" value="app" />
    </envs>
    <option name="myCustomStartScript" value="import sys&#10;sys.path.extend([WORKING_DIR_AND_PYTHON_PATHS])&#10;from flask.cli import ScriptInfo&#10;locals().update(ScriptInfo(create_app=None).load_app().make_shell_context())&#10;print(&quot;Python %s on %s\nApp: %s [%s]\nInstance: %s&quot; % (sys.version, sys.platform, app.import_name, app.env, app.instance_path))" />
    <option name="myEnvs">
      <map>
        <entry key="FLASK_APP" value="app" />
      </map>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/tiaozhanbei-1" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 2
}</component>
  <component name="ProjectId" id="2wxN2KWQ70UsQmIkDejpq8bPl6S" />
  <component name="ProjectViewState">
    <option name="autoscrollToSource" value="true" />
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Python 测试.Python tests for final_code_check.test_imports.executor&quot;: &quot;Run&quot;,
    &quot;Python 测试.Python tests for quick_excel_test.test_excel_queries.executor&quot;: &quot;Run&quot;,
    &quot;Python 测试.Python tests for quick_test.test_time_range_detection.executor&quot;: &quot;Run&quot;,
    &quot;Python 测试.Python tests for test_api.test_qwen_api.executor&quot;: &quot;Run&quot;,
    &quot;Python 测试.Python tests for test_enhanced_excel.test_enhanced_description.executor&quot;: &quot;Run&quot;,
    &quot;Python 测试.Python tests for test_excel_optimization.test_excel_processing.executor&quot;: &quot;Run&quot;,
    &quot;Python 测试.Python tests for test_fixed_excel.test_fixed_description.executor&quot;: &quot;Run&quot;,
    &quot;Python 测试.Python tests for test_smart_excel.test_smart_facility_extraction.executor&quot;: &quot;Run&quot;,
    &quot;Python 测试.Python tests for test_time_range_retriever.test_time_range_extraction.executor&quot;: &quot;Run&quot;,
    &quot;Python 测试.Python tests for test_time_series.test_time_series_summary.executor&quot;: &quot;Run&quot;,
    &quot;Python 测试.Python tests in diagnose_data_missing.py.executor&quot;: &quot;Run&quot;,
    &quot;Python 测试.Python tests in diagnose_rebuilt_kb.py.executor&quot;: &quot;Run&quot;,
    &quot;Python 测试.Python tests in fix_jan1_issue.py.executor&quot;: &quot;Run&quot;,
    &quot;Python 测试.Python tests in quick_test.py.executor&quot;: &quot;Run&quot;,
    &quot;Python 测试.Python tests in test_enhanced_excel.py.executor&quot;: &quot;Run&quot;,
    &quot;Python 测试.Python tests in test_time_fix.py.executor&quot;: &quot;Run&quot;,
    &quot;Python 测试.Python 测试 (excel_query_optimizer.py 内).executor&quot;: &quot;Run&quot;,
    &quot;Python 测试.Python 测试 (test_cache_effectiveness.py 内).executor&quot;: &quot;Run&quot;,
    &quot;Python 测试.Python 测试 (test_optimization.py 内).executor&quot;: &quot;Run&quot;,
    &quot;Python 测试.test_api.test_zhipu_api 的 Python 测试.executor&quot;: &quot;Run&quot;,
    &quot;Python 测试.test_integration.test_excel_direct_query 的 Python 测试.executor&quot;: &quot;Run&quot;,
    &quot;Python 测试.test_optimization.test_model_loading 的 Python 测试.executor&quot;: &quot;Run&quot;,
    &quot;Python.123.executor&quot;: &quot;Run&quot;,
    &quot;Python.MyData.executor&quot;: &quot;Run&quot;,
    &quot;Python.app.executor&quot;: &quot;Run&quot;,
    &quot;Python.build_excel_only_kb.executor&quot;: &quot;Run&quot;,
    &quot;Python.build_kb_optimized.executor&quot;: &quot;Run&quot;,
    &quot;Python.build_knowledge_base (1).executor&quot;: &quot;Run&quot;,
    &quot;Python.build_knowledge_base.executor&quot;: &quot;Run&quot;,
    &quot;Python.build_knowledge_base_deepdoc.executor&quot;: &quot;Run&quot;,
    &quot;Python.build_knowledge_base_optimized.executor&quot;: &quot;Run&quot;,
    &quot;Python.build_knowledge_base_quick_test.executor&quot;: &quot;Run&quot;,
    &quot;Python.build_simple.executor&quot;: &quot;Run&quot;,
    &quot;Python.check_deepdoc.executor&quot;: &quot;Run&quot;,
    &quot;Python.check_excel_data.executor&quot;: &quot;Run&quot;,
    &quot;Python.check_optimization_status.executor&quot;: &quot;Run&quot;,
    &quot;Python.check_ragflow_path.executor&quot;: &quot;Run&quot;,
    &quot;Python.data_test.executor&quot;: &quot;Run&quot;,
    &quot;Python.down (1).executor&quot;: &quot;Run&quot;,
    &quot;Python.down.executor&quot;: &quot;Run&quot;,
    &quot;Python.download.executor&quot;: &quot;Run&quot;,
    &quot;Python.embedding.executor&quot;: &quot;Run&quot;,
    &quot;Python.fix_datrie_imports.executor&quot;: &quot;Run&quot;,
    &quot;Python.fix_deepdoc_all.executor&quot;: &quot;Run&quot;,
    &quot;Python.fix_deepdoc_imports.executor&quot;: &quot;Run&quot;,
    &quot;Python.get_data01.executor&quot;: &quot;Run&quot;,
    &quot;Python.install_cryptodome.executor&quot;: &quot;Run&quot;,
    &quot;Python.install_datrie.executor&quot;: &quot;Run&quot;,
    &quot;Python.install_deepdoc.executor&quot;: &quot;Run&quot;,
    &quot;Python.install_dependencies.executor&quot;: &quot;Run&quot;,
    &quot;Python.install_missing_deps.executor&quot;: &quot;Run&quot;,
    &quot;Python.install_more_deps.executor&quot;: &quot;Run&quot;,
    &quot;Python.install_tablerag.executor&quot;: &quot;Run&quot;,
    &quot;Python.main.executor&quot;: &quot;Run&quot;,
    &quot;Python.page.executor&quot;: &quot;Run&quot;,
    &quot;Python.quick_startup_test.executor&quot;: &quot;Run&quot;,
    &quot;Python.quick_test_fix.executor&quot;: &quot;Run&quot;,
    &quot;Python.rebuild_knowledge_base.executor&quot;: &quot;Run&quot;,
    &quot;Python.rebuild_knowledge_base_fast.executor&quot;: &quot;Run&quot;,
    &quot;Python.rebuild_knowledge_base_ultra_fast.executor&quot;: &quot;Run&quot;,
    &quot;Python.sample.executor&quot;: &quot;Run&quot;,
    &quot;Python.start.executor&quot;: &quot;Run&quot;,
    &quot;Python.start_app.executor&quot;: &quot;Run&quot;,
    &quot;Python.test (1).executor&quot;: &quot;Run&quot;,
    &quot;Python.test.executor&quot;: &quot;Run&quot;,
    &quot;Python.test01 (1).executor&quot;: &quot;Run&quot;,
    &quot;Python.test01.executor&quot;: &quot;Run&quot;,
    &quot;Python.test02 (1).executor&quot;: &quot;Run&quot;,
    &quot;Python.test02.executor&quot;: &quot;Run&quot;,
    &quot;Python.test04 (1).executor&quot;: &quot;Run&quot;,
    &quot;Python.test04.executor&quot;: &quot;Run&quot;,
    &quot;Python.test_6gb_compatibility.executor&quot;: &quot;Run&quot;,
    &quot;Python.test_deepdoc.executor&quot;: &quot;Run&quot;,
    &quot;Python.test_excel_queries.executor&quot;: &quot;Run&quot;,
    &quot;Python.test_gpu_quick.executor&quot;: &quot;Run&quot;,
    &quot;Python.test_tablerag_integration.executor&quot;: &quot;Run&quot;,
    &quot;Python.test_tablerag_simple.executor&quot;: &quot;Run&quot;,
    &quot;Python.token_test.executor&quot;: &quot;Run&quot;,
    &quot;Python.train.executor&quot;: &quot;Run&quot;,
    &quot;Python.validate_tablerag_setup.executor&quot;: &quot;Run&quot;,
    &quot;Python.verify_jan1_data.executor&quot;: &quot;Run&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;main&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/pythonProject/tiaozhanbei-plus/data&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.pluginManager&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\pythonProject\tiaozhanbei-plus\data" />
      <recent name="D:\pythonProject\tiaozhanbei-plus\mineru-model" />
      <recent name="D:\pythonProject\tiaozhanbei-plus" />
      <recent name="D:\pythonProject\tiaozhanbei\lightgbm_models" />
      <recent name="C:\Users\<USER>\PycharmProjects\pythonProject" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="C:\Users\<USER>\PycharmProjects\pythonProject\tiaozhanbei" />
      <recent name="C:\Users\<USER>\PycharmProjects\pythonProject\demo_25\data" />
    </key>
  </component>
  <component name="RunManager" selected="Python 测试.Python tests for test_api.test_qwen_api">
    <configuration name="build_knowledge_base (1)" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="pythonProject" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/tiaozhanbei-2" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/tiaozhanbei-2/build_knowledge_base.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="build_knowledge_base" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="pythonProject" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/tiaozhanbei-1" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/tiaozhanbei-1/build_knowledge_base.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="start_app" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="pythonProject" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/tiaozhanbei-2" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/tiaozhanbei-2/start_app.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="Python tests for test_api.test_qwen_api" type="tests" factoryName="Autodetect" temporary="true" nameIsGenerated="true">
      <module name="pythonProject" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/tiaozhanbei-2" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="_new_additionalArguments" value="&quot;&quot;" />
      <option name="_new_target" value="&quot;test_api.test_qwen_api&quot;" />
      <option name="_new_targetType" value="&quot;PYTHON&quot;" />
      <method v="2" />
    </configuration>
    <configuration name="test_api.test_zhipu_api 的 Python 测试" type="tests" factoryName="Autodetect" temporary="true" nameIsGenerated="true">
      <module name="pythonProject" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/tiaozhanbei-2" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="_new_additionalArguments" value="&quot;&quot;" />
      <option name="_new_target" value="&quot;test_api.test_zhipu_api&quot;" />
      <option name="_new_targetType" value="&quot;PYTHON&quot;" />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Python 测试.Python tests for test_api.test_qwen_api" />
        <item itemvalue="Python 测试.test_api.test_zhipu_api 的 Python 测试" />
        <item itemvalue="Python.start_app" />
        <item itemvalue="Python.build_knowledge_base (1)" />
        <item itemvalue="Python.build_knowledge_base" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-1d06a55b98c1-0b3e54e931b4-JavaScript-PY-241.18968.29" />
        <option value="bundled-python-sdk-f5635417b835-2767605e8bc2-com.jetbrains.pycharm.pro.sharedIndexes.bundled-PY-241.18968.29" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="736dcd87-0fb5-4a1e-8599-48c618c94e2c" name="更改" comment="" />
      <created>1746979056281</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1746979056281</updated>
      <workItem from="1746979059994" duration="628000" />
      <workItem from="1747205261978" duration="22000" />
      <workItem from="1747205312197" duration="633000" />
      <workItem from="1750259641454" duration="377000" />
      <workItem from="1750297136461" duration="1714000" />
      <workItem from="1750301865186" duration="11000" />
      <workItem from="1750302236664" duration="19000" />
      <workItem from="1750317098058" duration="9000" />
      <workItem from="1750327392536" duration="182000" />
      <workItem from="1750327683422" duration="200000" />
      <workItem from="1750328125854" duration="2000" />
      <workItem from="1750752567769" duration="30000" />
      <workItem from="1751273609972" duration="2230000" />
      <workItem from="1751280478310" duration="24827000" />
      <workItem from="1752569239084" duration="7333000" />
      <workItem from="1752583139726" duration="1656000" />
      <workItem from="1752593166322" duration="20436000" />
      <workItem from="1752666606105" duration="35000" />
      <workItem from="1752666672106" duration="7562000" />
      <workItem from="1752714105527" duration="7240000" />
      <workItem from="1752751360674" duration="5272000" />
      <workItem from="1752818968240" duration="10190000" />
      <workItem from="1752891156188" duration="609000" />
      <workItem from="1752894964302" duration="5748000" />
      <workItem from="1752911715333" duration="3214000" />
      <workItem from="1752915418851" duration="6966000" />
      <workItem from="1752932675032" duration="14860000" />
      <workItem from="1752998683255" duration="24344000" />
      <workItem from="1753064359395" duration="223000" />
      <workItem from="1753064597495" duration="4861000" />
      <workItem from="1753080304874" duration="7320000" />
      <workItem from="1753087852862" duration="4668000" />
      <workItem from="1753098259318" duration="23024000" />
      <workItem from="1753326462330" duration="3291000" />
      <workItem from="1753370208298" duration="16937000" />
      <workItem from="1753457877907" duration="3075000" />
      <workItem from="1753498741468" duration="12775000" />
      <workItem from="1753528557955" duration="985000" />
      <workItem from="1753540667670" duration="28000" />
      <workItem from="1753545697070" duration="12975000" />
      <workItem from="1753594774160" duration="5422000" />
      <workItem from="1753617016489" duration="1927000" />
      <workItem from="1753619078168" duration="18064000" />
      <workItem from="1753714966900" duration="13428000" />
      <workItem from="1753773612365" duration="1422000" />
      <workItem from="1753775329496" duration="1611000" />
      <workItem from="1753886485929" duration="923000" />
      <workItem from="1753967940145" duration="2084000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/pythonProject$build_knowledge_base__1_.coverage" NAME="build_knowledge_base (1) 覆盖结果" MODIFIED="1753767098996" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/tiaozhanbei-2" />
    <SUITE FILE_PATH="coverage/pythonProject$train.coverage" NAME="train 覆盖结果" MODIFIED="1751358115461" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/demo_02" />
    <SUITE FILE_PATH="coverage/pythonProject$build_simple.coverage" NAME="build_simple 覆盖结果" MODIFIED="1753592195741" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/tiaozhanbei-123" />
    <SUITE FILE_PATH="coverage/pythonProject$rebuild_knowledge_base.coverage" NAME="rebuild_knowledge_base 覆盖结果" MODIFIED="1753618240901" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/tiaozhanbei-2" />
    <SUITE FILE_PATH="coverage/pythonProject$start_app.coverage" NAME="start_app 覆盖结果" MODIFIED="1753774029479" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/tiaozhanbei-2" />
    <SUITE FILE_PATH="coverage/pythonProject$down.coverage" NAME="down 覆盖结果" MODIFIED="1752581043271" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/demo_22" />
    <SUITE FILE_PATH="coverage/pythonProject$build_knowledge_base_quick_test.coverage" NAME="build_knowledge_base_quick_test 覆盖结果" MODIFIED="1753589089051" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/tiaozhanbei-123" />
    <SUITE FILE_PATH="coverage/pythonProject$fix_deepdoc_all.coverage" NAME="fix_deepdoc_all 覆盖结果" MODIFIED="1753452280755" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/tiaozhanbei-plus" />
    <SUITE FILE_PATH="coverage/pythonProject$fix_deepdoc_imports.coverage" NAME="fix_deepdoc_imports 覆盖结果" MODIFIED="1753452124701" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/tiaozhanbei-plus" />
    <SUITE FILE_PATH="coverage/pythonProject$get_data01.coverage" NAME="get_data01 覆盖结果" MODIFIED="1752569449372" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/demo_22" />
    <SUITE FILE_PATH="coverage/pythonProject$rebuild_knowledge_base_fast.coverage" NAME="rebuild_knowledge_base_fast 覆盖结果" MODIFIED="1753617430448" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/tiaozhanbei-2" />
    <SUITE FILE_PATH="coverage/pythonProject$test_gpu_quick.coverage" NAME="test_gpu_quick 覆盖结果" MODIFIED="1753591459095" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/tiaozhanbei-123" />
    <SUITE FILE_PATH="coverage/pythonProject$MyData.coverage" NAME="MyData 覆盖结果" MODIFIED="1751283486706" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/demo_02" />
    <SUITE FILE_PATH="coverage/pythonProject$fix_datrie_imports.coverage" NAME="fix_datrie_imports 覆盖结果" MODIFIED="1753453704598" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/tiaozhanbei-plus" />
    <SUITE FILE_PATH="coverage/pythonProject$123.coverage" NAME="123 覆盖结果" MODIFIED="1746979074616" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/pythonProject$install_cryptodome.coverage" NAME="install_cryptodome 覆盖结果" MODIFIED="1753452968686" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/tiaozhanbei-plus" />
    <SUITE FILE_PATH="coverage/pythonProject$test_excel_queries.coverage" NAME="test_excel_queries 覆盖结果" MODIFIED="1753583229887" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/tiaozhanbei-plus" />
    <SUITE FILE_PATH="coverage/pythonProject$test01__1_.coverage" NAME="test01 (1) 覆盖结果" MODIFIED="1752635693451" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/demo_24" />
    <SUITE FILE_PATH="coverage/pythonProject$install_dependencies.coverage" NAME="install_dependencies 覆盖结果" MODIFIED="1753586570034" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/tiaozhanbei-123" />
    <SUITE FILE_PATH="coverage/pythonProject$test02__1_.coverage" NAME="test02 (1) 覆盖结果" MODIFIED="1752647230969" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/demo_24" />
    <SUITE FILE_PATH="coverage/pythonProject$quick_startup_test.coverage" NAME="quick_startup_test 覆盖结果" MODIFIED="1753628129310" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/tiaozhanbei-1" />
    <SUITE FILE_PATH="coverage/pythonProject$main.coverage" NAME="main 覆盖结果" MODIFIED="1753321237264" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Transformer_Example-main" />
    <SUITE FILE_PATH="coverage/pythonProject$build_excel_only_kb.coverage" NAME="build_excel_only_kb 覆盖结果" MODIFIED="1753582094286" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/tiaozhanbei-plus" />
    <SUITE FILE_PATH="coverage/pythonProject$data_test.coverage" NAME="data_test 覆盖结果" MODIFIED="1751282806075" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/demo_02" />
    <SUITE FILE_PATH="coverage/pythonProject$down__1_.coverage" NAME="down (1) 覆盖结果" MODIFIED="1752629891850" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/demo_23" />
    <SUITE FILE_PATH="coverage/pythonProject$install_more_deps.coverage" NAME="install_more_deps 覆盖结果" MODIFIED="1753453165160" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/tiaozhanbei-plus" />
    <SUITE FILE_PATH="coverage/pythonProject$page.coverage" NAME="page 覆盖结果" MODIFIED="1752897333215" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/demo_25" />
    <SUITE FILE_PATH="coverage/pythonProject$download.coverage" NAME="download 覆盖结果" MODIFIED="1753066454414" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/llms" />
    <SUITE FILE_PATH="coverage/pythonProject$test04__1_.coverage" NAME="test04 (1) 覆盖结果" MODIFIED="1752825488061" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/demo_25" />
    <SUITE FILE_PATH="coverage/pythonProject$sample.coverage" NAME="sample 覆盖结果" MODIFIED="1753340874007" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/tiaozhanbei/lightgbm_models" />
    <SUITE FILE_PATH="coverage/pythonProject$test_6gb_compatibility.coverage" NAME="test_6gb_compatibility 覆盖结果" MODIFIED="1753546657316" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/tiaozhanbei-plus" />
    <SUITE FILE_PATH="coverage/pythonProject$check_excel_data.coverage" NAME="check_excel_data 覆盖结果" MODIFIED="1753583173160" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/tiaozhanbei-plus" />
    <SUITE FILE_PATH="coverage/pythonProject$install_datrie.coverage" NAME="install_datrie 覆盖结果" MODIFIED="1753453604047" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/tiaozhanbei-plus" />
    <SUITE FILE_PATH="coverage/pythonProject$check_deepdoc.coverage" NAME="check_deepdoc 覆盖结果" MODIFIED="1753452291486" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/tiaozhanbei-plus" />
    <SUITE FILE_PATH="coverage/pythonProject$start.coverage" NAME="start 覆盖结果" MODIFIED="1753591475093" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/tiaozhanbei-123" />
    <SUITE FILE_PATH="coverage/pythonProject$validate_tablerag_setup.coverage" NAME="validate_tablerag_setup 覆盖结果" MODIFIED="1753545842610" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/tiaozhanbei-plus" />
    <SUITE FILE_PATH="coverage/pythonProject$install_deepdoc.coverage" NAME="install_deepdoc 覆盖结果" MODIFIED="1753451489336" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/tiaozhanbei-plus" />
    <SUITE FILE_PATH="coverage/pythonProject$test04.coverage" NAME="test04 覆盖结果" MODIFIED="1752648168880" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/demo_24" />
    <SUITE FILE_PATH="coverage/pythonProject$app.coverage" NAME="app 覆盖结果" MODIFIED="1753502558147" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/tiaozhanbei-plus" />
    <SUITE FILE_PATH="coverage/pythonProject$install_missing_deps.coverage" NAME="install_missing_deps 覆盖结果" MODIFIED="1753546042186" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/tiaozhanbei-plus" />
    <SUITE FILE_PATH="coverage/pythonProject$test02.coverage" NAME="test02 覆盖结果" MODIFIED="1752631391161" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/demo_23" />
    <SUITE FILE_PATH="coverage/pythonProject$test_deepdoc.coverage" NAME="test_deepdoc 覆盖结果" MODIFIED="1753451518608" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/tiaozhanbei-plus" />
    <SUITE FILE_PATH="coverage/pythonProject$.coverage" NAME=" 覆盖结果" MODIFIED="1753774681742" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/tiaozhanbei-2" />
    <SUITE FILE_PATH="coverage/pythonProject$test__1_.coverage" NAME="test (1) 覆盖结果" MODIFIED="1753062846261" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/tiaozhanbei" />
    <SUITE FILE_PATH="coverage/pythonProject$test_tablerag_integration.coverage" NAME="test_tablerag_integration 覆盖结果" MODIFIED="1753546102999" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/tiaozhanbei-plus" />
    <SUITE FILE_PATH="coverage/pythonProject$test_tablerag_simple.coverage" NAME="test_tablerag_simple 覆盖结果" MODIFIED="1753546074004" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/tiaozhanbei-plus" />
    <SUITE FILE_PATH="coverage/pythonProject$build_knowledge_base_deepdoc.coverage" NAME="build_knowledge_base_deepdoc 覆盖结果" MODIFIED="1753587397060" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/tiaozhanbei-123" />
    <SUITE FILE_PATH="coverage/pythonProject$build_kb_optimized.coverage" NAME="build_kb_optimized 覆盖结果" MODIFIED="1753582453825" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/tiaozhanbei-plus" />
    <SUITE FILE_PATH="coverage/pythonProject$rebuild_knowledge_base_ultra_fast.coverage" NAME="rebuild_knowledge_base_ultra_fast 覆盖结果" MODIFIED="1753618582400" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/tiaozhanbei-2" />
    <SUITE FILE_PATH="coverage/pythonProject$verify_jan1_data.coverage" NAME="verify_jan1_data 覆盖结果" MODIFIED="1753672378640" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/tiaozhanbei-1" />
    <SUITE FILE_PATH="coverage/pythonProject$embedding.coverage" NAME="embedding 覆盖结果" MODIFIED="1752593415217" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/demo_22" />
    <SUITE FILE_PATH="coverage/pythonProject$install_tablerag.coverage" NAME="install_tablerag 覆盖结果" MODIFIED="1753545806632" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/tiaozhanbei-plus" />
    <SUITE FILE_PATH="coverage/pythonProject$test.coverage" NAME="test 覆盖结果" MODIFIED="1753061729774" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/tiaozhanbei/storage" />
    <SUITE FILE_PATH="coverage/pythonProject$test01.coverage" NAME="test01 覆盖结果" MODIFIED="1752594536720" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/demo_23" />
    <SUITE FILE_PATH="coverage/pythonProject$check_ragflow_path.coverage" NAME="check_ragflow_path 覆盖结果" MODIFIED="1753453465865" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/tiaozhanbei-plus" />
    <SUITE FILE_PATH="coverage/pythonProject$check_optimization_status.coverage" NAME="check_optimization_status 覆盖结果" MODIFIED="1753621998039" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/tiaozhanbei-1" />
    <SUITE FILE_PATH="coverage/pythonProject$build_knowledge_base_optimized.coverage" NAME="build_knowledge_base_optimized 覆盖结果" MODIFIED="1753589420296" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/tiaozhanbei-123" />
    <SUITE FILE_PATH="coverage/pythonProject$token_test.coverage" NAME="token_test 覆盖结果" MODIFIED="1751360876128" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/demo_02" />
    <SUITE FILE_PATH="coverage/pythonProject$build_knowledge_base.coverage" NAME="build_knowledge_base 覆盖结果" MODIFIED="1753718831352" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/tiaozhanbei-1" />
    <SUITE FILE_PATH="coverage/pythonProject$quick_test_fix.coverage" NAME="quick_test_fix 覆盖结果" MODIFIED="1753583717460" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/tiaozhanbei-plus" />
  </component>
</project>